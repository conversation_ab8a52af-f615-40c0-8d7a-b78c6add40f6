import 'package:wasla/src/core/utils/app_constants.dart';
import 'package:wasla/src/core/utils/loading_widget.dart';
import 'package:wasla/src/pages/widgets/no_internet_connection_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xr_helper/xr_helper.dart' show Log;

import '../core/config/app_config.dart';

class WebViewPage extends StatefulWidget {
  const WebViewPage({super.key});

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  // Method to handle custom URL schemes
  Future<bool> _handleCustomUrlSchemes(String url) async {
    // List of custom schemes that should be opened externally
    final customSchemes = [
      'whatsapp://',
      'mailto:',
      'tel:',
      'sms:',
      'twitter://',
      'fb://',
      'm.facebook.com/',
      'facebook.com/',
      'https://www.facebook.com',
      'https://facebook.com',
      'https://m.facebook.com',
      'linkedin://',
      'instagram://',
      'tg://', // Telegram
      'viber://',
      'skype:',
      'intent://', // Android intents
    ];

    // Check if the URL starts with any custom scheme
    for (String scheme in customSchemes) {
      if (url.startsWith(scheme)) {
        try {
          final uri = Uri.parse(url);
          if (await canLaunchUrl(uri)) {
            await launchUrl(
              uri,
              mode: LaunchMode.externalApplication,
            );
            return true; // Prevent WebView from loading this URL
          }
        } catch (e) {
          Log.e('Error launching URL: $url, Error: $e');
        }
        return true; // Still prevent WebView from loading even if launch failed
      }
    }

    // Also handle print functionality
    if (url.contains('javascript:window.print()') || url.contains('print()')) {
      // For print, we can show a message or handle it differently
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Print functionality is not available in the app'),
          duration: Duration(seconds: 2),
        ),
      );
      return true; // Prevent WebView from loading
    }

    return false; // Let WebView handle normal URLs
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppConfig>(
      builder: (context, appConfig, child) {
        return WillPopScope(
          onWillPop: () async {
            if (appConfig.webViewController != null) {
              final canGoBack = await appConfig.webViewController!.canGoBack();
              if (canGoBack) {
                appConfig.webViewController!.goBack();
                return false;
              }
            }
            return false;
          },
          child: Scaffold(
            backgroundColor: Colors.white,
            body: SafeArea(
              child: Builder(builder: (context) {
                if (!appConfig.hasInternet) {
                  return const NoInternetConnectionWidget();
                }

                if (appConfig.isLoading) {
                  return const Center(
                    child: LoadingWidget(),
                  );
                }

                // final token = OneSignalNotificationService.getUserId();

                return InAppWebView(
                  onWebViewCreated: appConfig.onWebViewCreated,
                  pullToRefreshController: appConfig.pullToRefreshController,
                  initialSettings: InAppWebViewSettings(
                    useShouldOverrideUrlLoading: true,
                    mediaPlaybackRequiresUserGesture: false,
                    allowsInlineMediaPlayback: true,
                  ),
                  // Handle custom URL schemes
                  shouldOverrideUrlLoading:
                      (controller, navigationAction) async {
                    final url = navigationAction.request.url.toString();

                    // Check if this is a custom scheme that should be handled externally
                    if (await _handleCustomUrlSchemes(url)) {
                      return NavigationActionPolicy.CANCEL;
                    }

                    // Allow normal navigation
                    return NavigationActionPolicy.ALLOW;
                  },
                  // onLoadStop: (controller, url) async {
                  //   await appConfig.addTokenToLogin(controller: controller);
                  // },
                  // onProgressChanged: (controller, progress) async {
                  //   if (progress == 100) {
                  //     await appConfig.addTokenToLogin(controller: controller);
                  //   }
                  // },
                  // onUpdateVisitedHistory:
                  //     (controller, url, androidIsReload) async {
                  //   await appConfig.addTokenToLogin(controller: controller);
                  // },
                  onReceivedServerTrustAuthRequest:
                      (controller, challenge) async {
                    return ServerTrustAuthResponse(
                        action: ServerTrustAuthResponseAction.PROCEED);
                  },
                  initialUrlRequest: URLRequest(
                    url: WebUri.uri(
                      Uri.parse(AppConstants.appUrl),
                    ),
                  ),
                );
              }),
            ),
          ),
        );
      },
    );
  }
}
